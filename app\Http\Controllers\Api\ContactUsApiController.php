<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\BaseController;
use App\Models\ContactUs;
use AWS\CRT\HTTP\Request;

/**
 * Class ContactUsApiController.
 */
class ContactUsApiController extends BaseController
{


    /**
     * @OA\Post(
     *      path="/api/v1/contact-us",
     *      operationId="storeContactUs",
     *      tags={"Contact Us"},
     *      summary="Store contact us information",
     *      description="Store contact us data",
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/ContactUsStoreRequest")
     *      ),
     *      @OA\Response(
     *          response=201,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/ContactUsResource")
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      )
     * )
     */
    public function store(Request $request)
    {
        $contactUs = ContactUs::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'message' => $request->message
        ]);
        return $this->sendResponse($contactUs, 'Contact us message sent successfully.');
    }
}
