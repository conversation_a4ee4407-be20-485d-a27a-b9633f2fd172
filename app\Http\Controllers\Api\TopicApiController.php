<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\TopicStoreRequest;
use App\Http\Requests\TopicUpdateRequest;
use App\Http\Resources\TextContentResource;
use App\Http\Resources\TopicResource;
use App\Http\Resources\TopicVideoResource;
use App\Models\Topic;
use App\Models\TopicVideo;
use App\Services\Subject\SubjectService;
use App\Services\Topic\TopicService;

class TopicApiController extends BaseController
{
    public function __construct(
        private TopicService $topicService,
        private SubjectService $subjectService)
    {
       // $this->middleware('app.role:ROLE_STUDENT,ROLE_TEACHER,ROLE_CUSTOMER');
    }

    /**
     * @OA\Get(
     *      path="/api/v1/topics/subjects/{subject_id}",
     *      operationId="getTopicsBySubjectId",
     *      tags={"Topics"},
     *      summary="Get list of topics information",
     *      security={{"passport": {}}},
     *      description="Returns list of topics data",
     *      @OA\Parameter(
     *          name="subject_id",
     *          description="Subject id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="string",
     *              example="3a425277-69c1-4a5f-97a2-695bc976fd32",
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/TopicCollectionResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function getTopicsBySubjectId($subject_id)
    {
        $topics = $this->subjectService->getTopicsBySubjectId($subject_id);
        
        if (is_null($topics)) {
            return $this->sendError('Topics do not exist.');
        }

        $response = array();
        foreach ($topics as $topic) {
            
            $item['id'] = $topic->id;
            $item['image'] = $topic->image;
            $item['model_image'] = $topic->model_image;
            $item['android_model'] = $topic->android_model;
            $item['ios_model'] = $topic->ios_model;
            $item['web_model'] = $topic->web_model;
            $item['addressable_ios'] = $topic->addressable_ios;
            $item['addressable_android'] = $topic->addressable_android;
            $item['is_quiz_required'] = $topic->is_quiz_required;
            $item['is_model_required'] = $topic->is_model_required;
            $item['has_game'] = $topic->has_game;
            $item['has_ar_drawing'] = $topic->has_ar_drawing;
            $item['videos'] = is_null($topic->videos) ? false : true;
            $item['quizzes'] = is_null($topic->quizzes) ? false : true;
            $item['text_content'] = new TextContentResource($topic->text_content);
            array_push($response, $item);
        }

        return $response; //$this->sendResponse($response, 'Topics fetched.');
    }

    public function getVideosByTopicId($topic_id)
    {
        $videos = TopicVideo::where('topic_id', $topic_id)->get();
        if (is_null($videos)) {
            return $this->sendError('Videos do not exist.');
        }
        return $this->sendResponse(TopicVideoResource::collection($videos), 'Videos fetched.');
    }

    /**
     * @OA\Get(
     *      path="/api/v1/topics/search/{search}/{lang?}",
     *      operationId="getSearchResults",
     *      tags={"Topics"},
     *      summary="Search list of topics information",
     *      security={{"passport": {}}},
     *      description="Returns list of topics data",
     *      @OA\Parameter(
     *          name="search",
     *          description="Search keyword",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="string",
     *              example="keyword to search",
     *          )
     *      ),
     *      @OA\Parameter(
     *          name="lang",
     *          description="Language code",
     *          required=false,
     *          in="path",
     *          @OA\Schema(
     *              type="string",
     *              example="en",
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/TopicCollectionResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function getSearchResults($search, $lang = 'en')
    {
        $topics = $this->topicService->searchTopics($search, $lang);

        if (is_null($topics)) {
            return $this->sendError('Topics do not exist.');
        }
        return $this->sendResponse(TopicResource::collection($topics), 'Topics fetched.');
    }

    /**
     * @OA\Get(
     *      path="/api/v1/topics/languages/{lang}",
     *      operationId="getTranslatedTopics",
     *      tags={"Topics"},
     *      summary="Search list of translated topics information",
     *      security={{"passport": {}}},
     *      description="Returns list of topics data",
     *      @OA\Parameter(
     *          name="lang",
     *          description="Language code",
     *          required=false,
     *          in="path",
     *          @OA\Schema(
     *              type="string",
     *              example="en",
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/TopicCollectionResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function getTranslatedTopics($lang = 'en')
    {
        $topics = $this->topicService->getTopicsByLanguage($lang);

        if (is_null($topics)) {
            return $this->sendError('Topics do not exist.');
        }
        return $this->sendResponse(TopicResource::collection($topics), 'Topics fetched.');
    }


    /**
     * @OA\Get(
     *      path="/api/v1/topics",
     *      operationId="getAllTopics",
     *      tags={"Topics"},
     *      summary="Get list of topics information",
     *      security={{"passport": {}}},
     *      description="Returns list of topics data",
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/TopicCollectionResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function index()
    {
        $topics = $this->topicService->getAll();
        return $this->sendResponse(TopicResource::collection($topics), 'Topics fetched.');
    }

    /**
     * @OA\Post(
     *      path="/api/v1/topics",
     *      operationId="storeTopic",
     *      tags={"Topics"},
     *      summary="Create a new topic",
     *      security={{"passport": {}}},
     *      description="Returns newly created topic data",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               required={"name", "subject_id"},
     *               @OA\Property(property="name", type="text", description="Set the name of the topic"),
     *               @OA\Property(property="subject_id", type="text", description="Set the subject_id of the topic"),
     *               @OA\Property(property="ar_key", type="text", description="Set the ar_key if any of the topic"),
     *               @OA\Property(property="image", type="file", description="Set the image of the topic"),
     *               @OA\Property(property="android_model", type="file", description="Set the android ar model of the topic"),
     *               @OA\Property(property="ios_model", type="file", description="Set the ios ar model of the topic"),
     *               @OA\Property(property="web_model", type="file", description="Set the web model of the topic"),
     *               @OA\Property(property="is_quiz_required", type="boolean", nullable="true", description="Set the quiz flag of the topic"),
     *               @OA\Property(property="is_model_required", type="boolean", nullable="true", description="Set the models flag of the topic"),
     *               @OA\Property(property="has_game", type="boolean", nullable="true", description="Set the game flag of the topic"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/TopicResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function store(TopicStoreRequest $request)
    {
        $topic = $this->topicService->storeTopic($request);
        return $this->sendResponse(new TopicResource($topic), 'Topic created.');
    }

    /**
     * @OA\Get(
     *      path="/api/v1/topics/{id}",
     *      operationId="getTopic",
     *      tags={"Topics"},
     *      summary="Get topic information",
     *      security={{"passport": {}}},
     *      description="Returns topic data",
     *      @OA\Parameter(
     *          name="id",
     *          description="Topic id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="string",
     *              example="3a425277-69c1-4a5f-97a2-695bc976fd32",
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/TopicResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function show($id)
    {
        $topic = $this->topicService->getById($id);

        if (is_null($topic)) {
            return $this->sendError('Topic does not exist.');
        }
        return $this->sendResponse(new TopicResource($topic), 'Topic fetched.');
    }

    /**
     * @OA\Put(
     *      path="/api/v1/topics",
     *      operationId="updateTopic",
     *      tags={"Topics"},
     *      summary="Update an existing topic",
     *      security={{"passport": {}}},
     *      description="Returnsupdated topic data",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               required={"name", "subject_id"},
     *               @OA\Property(property="name", type="text", description="Update the name of the topic"),
     *               @OA\Property(property="subject_id", type="text", description="Update the subject_id of the topic"),
     *               @OA\Property(property="ar_key", type="text", description="Update the ar_key if any of the topic"),
     *               @OA\Property(property="image", type="file", description="Update the image of the topic"),
     *               @OA\Property(property="android_model", type="file", description="Update the android ar model of the topic"),
     *               @OA\Property(property="ios_model", type="file", description="Update the ios ar model of the topic"),
     *               @OA\Property(property="web_model", type="file", description="Update the web model of the topic"),
     *               @OA\Property(property="is_quiz_required", type="boolean", nullable="true", description="Update the quiz flag of the topic"),
     *               @OA\Property(property="is_model_required", type="boolean", nullable="true", description="Update the models flag of the topic"),
     *               @OA\Property(property="has_game", type="boolean", nullable="true", description="Update the game flag of the topic"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/TopicResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function update(TopicUpdateRequest $request, Topic $topic)
    {
        $topic = $this->topicService->updateTopic($request);

        return $this->sendResponse(new TopicResource($topic), 'Topic updated.');
    }

    /**
     * @OA\Delete(
     *      path="/api/v1/topics",
     *      operationId="deleteTopic",
     *      tags={"Topics"},
     *      summary="Delete a topic",
     *      security={{"passport": {}}},
     *      description="Returns deleted message",
     *     @OA\RequestBody(
     *        @OA\JsonContent(ref="#/components/schemas/Topic")
     *     ),
     *
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/DeleteResponse")
     *       ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      )
     * )
     */
    public function destroy(Topic $topic)
    {
        $this->topicService->deleteById($topic->id);
        return $this->sendResponse([], 'Topic deleted.');
    }
}
