<?php

namespace App\Repository\Eloquent;

use App\Models\Subject;
use App\Repository\SubjectRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class SubjectRepository extends BaseRepository implements SubjectRepositoryInterface
{
    /**
     * @var Model
     */
    protected $model;

    /**
     * BaseRepository constructor.
     *
     * @param Model $model
     */
    public function __construct(Subject $model)
    {
        $this->model = $model;
    }

    public function fetchTopicsBySubjectId($subject_id)
    {
        $subject = Subject::find($subject_id);
        if (!$subject) {
            return null;
        }
        return $subject->topics()->jsonPaginate();
    }

}
