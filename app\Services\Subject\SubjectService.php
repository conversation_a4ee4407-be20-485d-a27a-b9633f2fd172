<?php


namespace App\Services\Subject;

use App\DataTransferObjects\Subject\CreateSubject;
use App\DataTransferObjects\Subject\DeleteSubject;
use App\DataTransferObjects\Subject\UpdateSubject;
use App\DataTransferObjects\TextContent\CreateTextContent;
use App\DataTransferObjects\TextContent\DeleteTextContent;
use App\DataTransferObjects\TextContent\UpdateTextContent;
use App\Helpers\Helper;
use App\Http\Requests\SubjectStoreRequest;
use App\Http\Requests\SubjectUpdateRequest;
use App\Repository\SubjectRepositoryInterface;
use App\Services\TextContent\CreateTextContentService;
use App\Services\TextContent\DeleteTextContentService;
use App\Services\TextContent\UpdateTextContentService;

class SubjectService
{
    private $subjectRepository;

    public function __construct(
        private CreateSubjectService $createSubjectService,
        private UpdateSubjectService $updateSubjectService,
        private DeleteSubjectService $deleteSubjectService,
        private CreateTextContentService $createTextContentService,
        private UpdateTextContentService $updateTextContentService,
        private DeleteTextContentService $deleteTextContentService,
        SubjectRepositoryInterface $subjectRepository)
    {

        $this->subjectRepository = $subjectRepository;

    }

    public function getAll()
    {
        return $this->subjectRepository->all(['*'], ['text_content', 'school_class']);
    }

    

    public function getById($id)
    {
        return $this->subjectRepository->findById($id, ['*'], ['text_content']);
    }

    public function storeSubject(SubjectStoreRequest $request)
    {
        $createTextContent = CreateTextContent::make(
            $request->name,
            $request->description,
            Helper::defaultLanguage()->id);

        $textContent = $this->createTextContentService->handle($createTextContent);

        $createSubject = CreateSubject::fromRequest($request);
        $subject = $this->createSubjectService->handle($createSubject);

        $subject->text_content()->associate($textContent);
        $subject->save();

        return $subject;
    }

    public function updateSubject(SubjectUpdateRequest $request)
    {
        $updateSubject = UpdateSubject::fromRequest($request, $request->id);
        $subject = $this->updateSubjectService->handle($updateSubject);
        $updateTextContent = UpdateTextContent::make(
            $request->name,
            $request->description,
            Helper::defaultLanguage()->id,
            $subject->text_content_id);

        $this->updateTextContentService->handle($updateTextContent);

        return $subject;
    }

    public function getTopicsBySubjectId($subject_id)
    {
        return $this->subjectRepository->fetchTopicsBySubjectId($subject_id);
    }

    public function deleteById($id)
    {
        $deleteSubject = DeleteSubject::make($id);
        $deletedSubject = $this->deleteSubjectService->handle($deleteSubject);
        $deleteTextContent = DeleteTextContent::make($deletedSubject->text_content_id);
        $deletedTextContent = $this->deleteTextContentService->handle($deleteTextContent);
        return $deletedSubject;
    }
}
