{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2||^8.2", "aws/aws-sdk-php": "^3.257", "darkaonline/l5-swagger": "^8.5", "doctrine/dbal": "^3.6", "elegantweb/sanitizer": "^2.1", "guzzlehttp/guzzle": "^7.5", "laravel/framework": "^9.19", "laravel/passport": "^11.8", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "laravel/ui": "^4.1", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "pbmedia/laravel-ffmpeg": "^8.3", "psr/simple-cache": "^1.0", "spatie/laravel-multitenancy": "^3.0", "spatie/laravel-options": "^1.1", "spatie/laravel-permission": "^5.10", "srmklive/paypal": "~3.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}