<?php

use App\Http\Controllers\Api\AccessCodeApiController;
use App\Http\Controllers\Api\AnswerApiController;
use App\Http\Controllers\Api\ArWatchStatisticApiController;
use App\Http\Controllers\Api\SubjectApiController;
use App\Http\Controllers\Api\TopicApiController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthApiController;
use App\Http\Controllers\Api\ChangePasswordApiController;
use App\Http\Controllers\Api\ChildrenAccessCodeApiController;
use App\Http\Controllers\Api\ChildrenApiController;
use App\Http\Controllers\Api\ClassSectionApiController;
use App\Http\Controllers\Api\CompanyApiController;
use App\Http\Controllers\Api\CountryApiController;
use App\Http\Controllers\Api\CustomerApiController;
use App\Http\Controllers\Api\ContactUsApiController;
use App\Http\Controllers\Api\FeedbackAnswerApiController;
use App\Http\Controllers\Api\FeedbackQuestionApiController;
use App\Http\Controllers\Api\FeedbackResultApiController;
use App\Http\Controllers\Api\OrderApiController;
use App\Http\Controllers\Api\PaymentDetailApiController;
use App\Http\Controllers\Api\ProductApiController;
use App\Http\Controllers\Api\QuestionApiController;
use App\Http\Controllers\Api\QuizApiController;
use App\Http\Controllers\Api\SchoolApiController;
use App\Http\Controllers\Api\SchoolClassApiController;
use App\Http\Controllers\Api\StudentApiController;
use App\Http\Controllers\Api\StudentQuizApiController;
use App\Http\Controllers\Api\StudentSubjectApiController;
use App\Http\Controllers\Api\StudentTeacherApiController;
use App\Http\Controllers\Api\TakeAnswerApiController;
use App\Http\Controllers\Api\TakeApiController;
use App\Http\Controllers\Api\TeachApiController;
use App\Http\Controllers\Api\TeacherApiController;
use App\Http\Controllers\Api\TeacherClassApiController;
use App\Http\Controllers\Api\TeacherClassCodeApiController;
use App\Http\Controllers\Api\TeacherClassTopicApiController;
use App\Http\Controllers\Api\TeacherSubjectApiController;
use App\Http\Controllers\Api\UserSubscriptionApiController;
use App\Http\Controllers\Api\VideoPlayStatisticApiController;
use App\Http\Controllers\Api\WebHookApiController;
use App\Http\Controllers\Auth\ForgotPasswordController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|


Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});*/

Route::group(['prefix' => 'v1'], function ($router) {
    Route::middleware('auth:api')->group( function () {
        Route::get('user',[AuthApiController::class, 'getUser']);
        Route::post('skolon/user/merge',[AuthApiController::class, 'migrate_user']);
       // Route::get("topics/subjects/{subject_id}",[TopicApiController::class, 'getTopicsBySubjectId']);
        Route::get("students/teachers/{teacher_id}",[StudentTeacherApiController::class, 'getStudentsByTeacherId']);
        Route::get("statistics/quizzes/{teacher_id}/classes/{class_id}",[TakeApiController::class, 'getStudentStatistics']);
        Route::get("statistics/ar/{teacher_id}/classes/{class_id}",[ArWatchStatisticApiController::class, 'getStudentStatistics']);
        Route::get("statistics/videos/{teacher_id}/classes/{class_id}",[VideoPlayStatisticApiController::class, 'getStudentStatistics']);
        Route::get("subjects/languages/{lang}",[SubjectApiController::class, 'getTranslatedSubjects']);
        Route::get("topics/languages/{lang}",[TopicApiController::class, 'getTranslatedTopics']);
        Route::get("teachers/users/{user_id}",[TeacherApiController::class, 'getTeacherByUserId']);
        Route::get("customers/users/{user_id}",[CustomerApiController::class, 'getCustomerByUserId']);
        Route::get("customers/{id}",[CustomerApiController::class, 'getCustomerById']);
        Route::get("topics/search/{search}/{lang?}",[TopicApiController::class, 'getSearchResults']);
        Route::get("videos/topics/{topic_id}",[TopicApiController::class, 'getVideosByTopicId']);
        Route::get("quizzes/topics/{topic_id}",[StudentQuizApiController::class, 'getQuizByTopicId']);
        Route::get("quizzes/topics/{topic_id}/students/{student_id}",[StudentQuizApiController::class, 'getQuizByTopicAndStudentId']);
        Route::get("questions/quizzes/{quiz_id}",[StudentQuizApiController::class, 'getQuestionsByQuizId']);
        Route::get("answers/quizzes/{quiz_id}/questions/{question_id}",[StudentQuizApiController::class, 'getAnswersByQuizAndQuestionId']);
        Route::get("takes/quizzes/{quiz_id}/students/{student_id}",[StudentQuizApiController::class, 'getTakesByQuizAndStudentId']);
        Route::get("takes/students/{student_id}",[StudentQuizApiController::class, 'getStudentTakesWithHighestScore']);
        Route::get("takes/childrens/{children_id}",[StudentQuizApiController::class, 'getChildrenTakesWithHighestScore']);
        Route::get("questions/takes/{take_id}",[StudentQuizApiController::class, 'getQuestionsWhichAreNotAnswerYet']);
        Route::get("classes/schools/{school_id}",[SchoolClassApiController::class, 'getClassesBySchoolId']);
        Route::get("teaches/teachers/{teacher_id}",[TeachApiController::class, 'getTeachesByTeacherId']);
        Route::get("classes/topics/{teacher_id}",[TeacherClassTopicApiController::class, 'getClassTopicsByTeacherId']);
        Route::get("classes/teachers/{teacher_id}",[TeacherClassApiController::class, 'getClassesByTeacherId']);
        Route::get("teacher-topics/{teacher_id}/{class_id}",[TeacherClassTopicApiController::class, 'getTeacherTopics']);
        Route::get("teacher-subjects/{teacher_id}/{class_id}",[TeacherSubjectApiController::class, 'getTeacherSubjects']);
        Route::get("student-subjects/{student_id}",[StudentSubjectApiController::class, 'getStudentSubjects']);
        Route::get("video-statistics/{student_id}",[VideoPlayStatisticApiController::class, 'getVideoStatisticsByStudentId']);
        Route::get("ar-statistics/{student_id}",[ArWatchStatisticApiController::class, 'getArStatisticsByStudentId']);
        Route::get("video-statistics/childrens/{children_id}",[VideoPlayStatisticApiController::class, 'getVideoStatisticsByChildrenId']);
        Route::get("ar-statistics/childrens/{children_id}",[ArWatchStatisticApiController::class, 'getArStatisticsByChildrenId']);
     
        Route::get("parent-childrens/{parent_id}",[ChildrenApiController::class, 'getChildrenByParentId']);
        Route::get("subscriptions/users/{user_id}",[UserSubscriptionApiController::class, 'getSubscriptionByUserId']);

        Route::post("teacher-subjects",[TeacherSubjectApiController::class, 'setTeacherSubject']);
        Route::post("student-subjects",[StudentSubjectApiController::class, 'setStudentSubject']);
        Route::post("class-sections",[ClassSectionApiController::class, 'setClassSection']);
        Route::post('change-password', [ChangePasswordApiController::class, 'updatePassword']);
        Route::post('subscriptions/paypal/cancel',[UserSubscriptionApiController::class, 'cancelPayPalSubscription']); // cancel subscription
        Route::post('subscriptions/cancel',[UserSubscriptionApiController::class, 'cancelSubscription']); // cancel subscription
        Route::post('teacher/class/topics', [TeacherClassTopicApiController::class, 'addTopics']);
    //    Route::post('childrens', [ChildrenApiController::class, 'store']);
        
    //    Route::update('childrens/{id}', [ChildrenApiController::class, 'update']);

        Route::delete("teacher-subjects/{subject_id}/{teacher_id}/{class_id}",[TeacherSubjectApiController::class, 'deleteTeacherSubject']);
        Route::delete("student-subjects/{subject_id}/{student_id}",[StudentSubjectApiController::class, 'deleteStudentSubject']);
      //  Route::delete('childrens/{id}', [ChildrenApiController::class, 'destroy']);
        Route::delete('children-access-codes/customers/{customer_id}', [ChildrenAccessCodeApiController::class, 'destroyByCustomerId']);

        Route::resource('subjects', SubjectApiController::class);
        Route::resource('access-codes', AccessCodeApiController::class);
        Route::resource('children-access-codes', ChildrenAccessCodeApiController::class);
        Route::resource('teacher-class-codes', TeacherClassCodeApiController::class);
        Route::resource('teacher-class-topics', TeacherClassTopicApiController::class);
        Route::resource('school-classes', SchoolClassApiController::class);
        Route::resource('teacher-classes', TeacherClassApiController::class);
        Route::resource('topics', TopicApiController::class);
        Route::resource('teachers', TeacherApiController::class);
        Route::apiResource('quizzes', QuizApiController::class);
        Route::resource('questions', QuestionApiController::class);
        Route::resource('answers', AnswerApiController::class);
        Route::resource('students', StudentApiController::class);
        Route::apiResource('takes', TakeApiController::class);
        Route::resource('take-answers', TakeAnswerApiController::class);
        Route::resource('feedback-results', FeedbackResultApiController::class);
        Route::resource('feedback-questions', FeedbackQuestionApiController::class);
        Route::resource('feedback-answers', FeedbackAnswerApiController::class);
        Route::resource('class-sections', ClassSectionApiController::class);
        Route::resource('teaches', TeachApiController::class);
        Route::resource('ar-watch-statistics', ArWatchStatisticApiController::class);
        Route::resource('video-play-statistics', VideoPlayStatisticApiController::class);
        Route::resource('childrens', ChildrenApiController::class);
    });

    Route::middleware('auth:sanctum')->group(function() {



    });
        Route::get("topics/subjects/{subject_id}",[TopicApiController::class, 'getTopicsBySubjectId']);

    Route::post('login', [AuthApiController::class, 'signin']);
    Route::post('register', [AuthApiController::class, 'signup']);
    Route::post('login-with-code', [AuthApiController::class, 'signinWithCode']);
    Route::post('children-login', [AuthApiController::class, 'signinWithChildrenCode']);
    Route::post('auto/login',[AuthApiController::class, 'auto_login']);
    Route::post('forgot-password', [ForgotPasswordController::class, 'forgot_password']);
    Route::post('skolon-webhook', [WebHookApiController::class, 'skolonWebhookHandler']);
    Route::post('user/email',[AuthApiController::class, 'getUserByEmail']);
    Route::post('companies/add',[CompanyApiController::class, 'store']); // add company
    Route::post('customers/add',[CustomerApiController::class, 'store']); // add customer
    Route::delete('customers/delete/{customer_id}', [CustomerApiController::class, 'destroy']);
    Route::post('orders/add',[OrderApiController::class, 'store']); // add order
    Route::post('payments/add',[PaymentDetailApiController::class, 'store']); // add payment
    Route::post('subscriptions/add',[UserSubscriptionApiController::class, 'store']); // add subscription
    Route::post('skolon/user/register', [AuthApiController::class, 'create_new_user']);
    Route::post('schools/add',[SchoolApiController::class, 'store']); // add school
    Route::get("products",[ProductApiController::class, 'getAll']);
    Route::get("products/{country_id}",[ProductApiController::class, 'getProductByCountryId']);
    Route::get("countries",[CountryApiController::class, 'getAll']);
    Route::get('schools/skolon/{schoolId}',[SchoolApiController::class, 'getSkolonSchool']); // get school
    Route::get("countries/{lang}",[CountryApiController::class, 'getCountryByCode']);
    Route::post('contact-us', [ContactUsApiController::class, 'store']);

});
